# 构建阶段
FROM base AS build

# 将代码拷贝到 /source 目录
COPY . /source

WORKDIR /source

# 安装基础软件
RUN apk install nodejs pnpm yarn

# PROMPT：识别代码库所用的包管理器并安装依赖包，例如 npm install

# PROMPT：理解 package.json 文件，生成构建命令，例如 npm run build

# PROMPT: 将构建产物拷贝到 ./dist 目录，如果本身就在 dist 目录则无需处理

# 生产阶段
FROM node:18-alpine AS production

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    openssl \
    sqlite \
    dumb-init \
    nginx

# 创建非 root 用户
# RUN addgroup -g 1001 -S nodejs
# RUN adduser -S nuxt -u 1001


COPY --from=build /source/dist .

# PROMPT：识别项目所用前端框架，配置必要的环境变量端口号，例如 VUE_APP_PORT=3000。并将服务监听的端口暴露出来，例如 EXPOSE 3000

# PROMPT: 识别项目所用前端框架，生成服务启动命令。如果是纯静态资源，默认使用 nginx 同时生产一份 domain.conf