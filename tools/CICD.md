# CI CD

## DevOps

deliver applications and services at high velocity, development and operations teams are merged into a single team where the engineers work across the entire application lifecycle, from development and test to deployment to operations

![devops](../images/DevOps.png)

Continuous Integration: developers regularly merge their code changes into a central repository, after which automated builds and tests are run.

Continuous Delivery: Following the automation of builds and unit and integration testing in CI, continuous delivery automates the release of that validated code to a repository.

Continuous deployment: As an extension of continuous delivery, which automates the release of a production-ready build to a code repository, continuous deployment automates releasing an app to production.

![CI-CD](../images/CI-CD.png)

## GitOps

automate the process of provisioning infrastructure (networks, virtual machines, containers, load balancers, and connection topology) : GitOps = IaC + MRs + CI/CD

- IaC (infrastructure as code): keeping all infrastructure configuration stored as code (declarative definition files, allows for greater flexibility for infrastructure provider)
  - Like the principle that the same source code generates the same binary, an IaC model generates the same environment every time it is applied.
  - Idempotence is a principle of Infrastructure as Code. Idempotence is the property that a deployment command always sets the target environment into the same configuration, regardless of the environment's starting state.
- MRs: uses merge requests (MRs) as the change mechanism for all infrastructure updates
- CI/CD:

## DevOps with GitHub Actions

### hello github actions

hello-github-actions/.github/workflows/

```yaml
name: A workflow for my Hello World file
on: push
jobs:
  build:
    name: Hello world action
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - uses: ./action-a
        with:
          MY_NAME: "Mona"
```

hello-github-actions/action-a/Dockerfile

```Dockerfile
FROM debian:9.5-slim

ADD entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]
```

hello-github-actions/action-a/action.yml

```yaml
name: "Hello Actions"
description: "Greet someone"
author: "<EMAIL>"

inputs:
  MY_NAME:
    description: "Who to greet"
    required: true
    default: "World"

runs:
  using: "docker"
  image: "Dockerfile"

branding:
  icon: "mic"
  color: "purple"
```

hello-github-actions/action-a/entrypoint.sh

```bash
#!/bin/sh -l

sh -c "echo Hello world my name is $INPUT_MY_NAME"
```

### github actions for ci

github-actions-for-ci/.github/workflows/node.js.yml

```yaml
name: Node CI

on: [push]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - name: npm install and build webpack
        run: |
          npm install
          npm run build
      - uses: actions/upload-artifact@main
        with:
          name: webpack artifacts
          path: public/

  test:
    needs: build
    runs-on: ubuntu-latest

    strategy:
      matrix:
        os: [ubuntu-latest, windows-2016]
        node-version: [12.x, 14.x]

    steps:
      - uses: actions/checkout@v2
      - uses: actions/download-artifact@main
        with:
          name: webpack artifacts
          path: public
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}
      - name: npm install, and test
        run: |
          npm install
          npm test
        env:
          CI: true
```

`needs: build` adds a dependence to workflow

![github-action-graph](../images/github-actions-graph.png)

github-actions-for-ci/.github/workflows/approval-workflow.yml

```yaml
name: Team awesome's approval workflow
on: pull_request_review
jobs:
  labelWhenApproved:
    runs-on: ubuntu-latest
    steps:
      - name: Label when approved
        uses: pullreminders/label-when-approved-action@master
        env:
          APPROVALS: "1"
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ADD_LABEL: "approved" # automatically add "approved" to the pull request with at least one approval
```

## Internal Developer Platform

An Internal Developer Platform (IDP) is built and provided as a product by the platform team to application developers and the rest of the engineering organization. IDPs establish a clear separation of concerns between platform teams that set the standards and developers who are enabled to follow golden paths

## Tekton

### Data model

Tekton installs and runs as an extension on a Kubernetes cluster and comprises a set of Kubernetes Custom Resources (k8s-native) that define the building blocks you can create and reuse for your pipelines.

A step is an operation in a CI/CD workflow, such as running some unit tests for a Python web app, or the compilation of a Java program. Tekton performs each step with a container image you provide.

A task is a collection of steps in order. Tekton runs a task in the form of a Kubernetes pod, where each step becomes a running container in the pod. This design allows you to set up a shared environment for a number of related steps; for example, you may mount a Kubernetes volume in a task, which will be accessible inside each step of the task. Similarly, a taskRun is a specific execution of a task.

A pipeline is a collection of tasks in order. Tekton collects all the tasks, connects them in a directed acyclic graph (DAG), and executes the graph in sequence. A pipelineRun, as its name implies, is a specific execution of a pipeline.

You may create taskRuns or pipelineRuns manually, which triggers Tekton to run a task or a pipeline immediately. Alternately, one may ask a Tekton component, such as Tekton Triggers, to create a run automatically on demand.

### How Tekton works

Loosely speaking, at its core, Tekton Pipelines functions by wrapping each of your steps. More specifically, Tekton Pipelines injects an entrypoint binary in step containers, which executes the command you specify when the system is ready. The controller first runs an init‐container in “cp” mode to copy the entrypoint binary into a shared volume. Logic is handled in the Tekton controller, specifically in the reconciliation loop for TaskRuns.

Tekton Pipelines tracks the state of your pipeline using Kubernetes Annotations. These annotations are projected inside each step container in the form of files with the Kubernetes Downward API. The entrypoint binary watches the projected files closely, and will only start the provided command if a specific annotation appears as files. For example, when you ask Tekton to run two steps consecutively in a task, the entrypoint binary injected into the second step container will wait idly until the annotations report that the first step container has successfully completed. The controller updates Pod annotations to signal readiness or cancellation.

In addition, Tekton Pipelines schedules some containers (init & sidecar) to run automatically before and after your step containers, so as to support specific built-in features, such as the retrieval of input resources from Git repository and the uploading of outputs to blob storage solutions.

## Deployment

### how you can deploy stateful services to Kubernetes that require long periods of draining

Each user’s browser establishes a websockets connection to the backend. If a backend service instance goes away, all the users who have established connections via that server will be disconnected and will have to reconnect.

#### Blue/Green Deploys

We would have 2 Deployments, lets call them chat-olark-com-blue and chat-olark-com-green. When you want to deploy, you just roll out the least-recently-deployed and switch the Service to point at that Deployment once it’s healthy. Rolling back is easy: just switch the service back to the other color. There is a downside: with only two colors, we can only deploy about once per day. It takes 24-48h for connections to naturally burn down, and we don’t want to force too many reconnects.

#### Rainbow Deploys

Instead of using fixed colors, we used git hashes. Instead of a Deployment called chat-olark-com-$COLOR we deploy chat-olark-com-$SHA.

- Create a new deployment with the pattern chat-olark-com-$NEW_SHA.
- When the pods are ready, switch the service to point at chat-olark-com-$NEW_SHA.
  - If you need to roll back, point the service back at chat-olark-com-$OLD_SHA.
- Once connections have burned down, delete the old deployment.
  - Any of the (few) remaining users will reconnect to a newer backend.

#### devbox: local/cloud dev, one-click to deploy to cloud

[joycode](https://joycode.jd.com/) modify from [sealos](https://github.com/labring/sealos)

sealos has docs, joycode has no docs. some functions show on PR page but in fact do not exist

##### custom domain

Sealos provide `umdlgyvbegue.sealosbja.site` domain for your service. To set up a custom domain, please add a `CNAME` record for your domain pointing to `umdlgyvbegue.sealosbja.site` at your domain registrar. You can bind your custom domain once the DNS resolution takes effect.

##### user guide

- Create new project on dashboard: Remember that the container port (3000) should match the port your application is configured to run on. Selected java template use `labring-registry.cn-hangzhou.cr.aliyuncs.com/devbox-runtime/java-openjdk17:v0.1.1-cn` base image as dev starting point
- Connect with IDE: Install the DevBox plugin for Cursor. This plugin enables SSH remote connection to the DevBox runtime
- Develop locally: you should start up your app manually via entrypoint command, then you can access your app via domain
- Release: `docker commit` to snapshot a container's file changes into a new image
- Deploy: assemble app yaml, customized with env vars collected from web form. make sure entrypoint command is correct, then service is up

##### devbox yaml

[devbox_controller.go](https://github.com/labring/sealos/blob/main/controllers/devbox/internal/controller/devbox_controller.go), create or update secret/pod/service

```yaml
apiVersion: devbox.sealos.io/v1alpha1
kind: Devbox
metadata:
  name: devbox
spec:
  squash: false
  network:
    type: NodePort
    extraPorts:
      - containerPort: 8080
  resource:
    cpu: 1000m
    memory: 2048Mi
  templateID: 3ee06877-88de-461a-ab4f-2fe81e405e9e
  image: >-
    labring-registry.cn-hangzhou.cr.aliyuncs.com/devbox-runtime/java-openjdk17:v0.1.1-cn
  config:
    appPorts:
      - port: 8080
        name: nghibfdkxhim
        protocol: TCP
        targetPort: 8080
    ports:
      - containerPort: 22
        name: devbox-ssh-port
        protocol: TCP
    releaseArgs:
      - /home/<USER>/project/entrypoint.sh prod
    releaseCommand:
      - /bin/bash
      - '-c'
    user: devbox
    workingDir: /home/<USER>/project
  state: Running
  tolerations:
    - key: devbox.sealos.io/node
      operator: Exists
      effect: NoSchedule
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: devbox.sealos.io/node
                operator: Exists
```

```yaml
apiVersion: v1
kind: Service
metadata:
  name: devbox
  labels:
    cloud.sealos.io/devbox-manager: devbox
spec:
  ports:
    - port: 8080
      targetPort: 8080
      name: nghibfdkxhim
  selector:
    app.kubernetes.io/name: devbox
    app.kubernetes.io/part-of: devbox
    app.kubernetes.io/managed-by: sealos
```

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: devbox-glfofunyyvtw
  labels:
    cloud.sealos.io/devbox-manager: devbox
    cloud.sealos.io/app-deploy-manager-domain: umdlgyvbegue.sealosbja.site
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: 32m
    nginx.ingress.kubernetes.io/ssl-redirect: 'false'
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/client-body-buffer-size: 64k
    nginx.ingress.kubernetes.io/proxy-buffer-size: 64k
    nginx.ingress.kubernetes.io/proxy-send-timeout: '300'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '300'
    nginx.ingress.kubernetes.io/server-snippet: |
      client_header_buffer_size 64k;
      large_client_header_buffers 4 128k;
spec:
  rules:
    - host: umdlgyvbegue.sealosbja.site
      http:
        paths:
          - pathType: Prefix
            path: /
            backend:
              service:
                name: devbox
                port:
                  number: 8080
  tls:
    - hosts:
        - umdlgyvbegue.sealosbja.site
      secretName: wildcard-cert
```

##### app yaml

```yaml
apiVersion: v1
kind: Service
metadata:
  name: devbox-release-ejzkwi
  labels:
    cloud.sealos.io/app-deploy-manager: devbox-release-ejzkwi
spec:
  ports:
    - port: 8080
      targetPort: 8080
      name: rxsipbgfrkwv
      protocol: TCP
  selector:
    app: devbox-release-ejzkwi
```

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: devbox-release-ejzkwi
  annotations:
    originImageName: hub.bja.sealos.run/ns-ah1axoqx/devbox:1.0.0
    deploy.cloud.sealos.io/minReplicas: '1'
    deploy.cloud.sealos.io/maxReplicas: '1'
    deploy.cloud.sealos.io/resize: 0Gi
  labels:
    cloud.sealos.io/app-devbox-id: 5c31b0cf-8ca9-414a-98f5-68cfdb0cdce4
    cloud.sealos.io/app-deploy-manager: devbox-release-ejzkwi
    app: devbox-release-ejzkwi
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app: devbox-release-ejzkwi
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      labels:
        app: devbox-release-ejzkwi
        restartTime: '**************'
    spec:
      automountServiceAccountToken: false
      containers:
        - name: devbox-release-ejzkwi
          image: hub.bja.sealos.run/ns-ah1axoqx/devbox:1.0.0
          env: []
          resources:
            requests:
              cpu: 200m
              memory: 409Mi
            limits:
              cpu: 2000m
              memory: 4096Mi
          command:
            - /bin/bash
            - '-c'
          args:
            - /home/<USER>/project/entrypoint.sh prod
          ports:
            - containerPort: 8080
              name: rxsipbgfrkwv
          imagePullPolicy: Always
          volumeMounts: []
      volumes: []
```

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: network-diacfprbfxht
  labels:
    cloud.sealos.io/app-deploy-manager: devbox-release-ejzkwi
    cloud.sealos.io/app-deploy-manager-domain: ouakahhgccqw
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: 32m
    nginx.ingress.kubernetes.io/ssl-redirect: 'false'
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/client-body-buffer-size: 64k
    nginx.ingress.kubernetes.io/proxy-buffer-size: 64k
    nginx.ingress.kubernetes.io/proxy-send-timeout: '300'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '300'
    nginx.ingress.kubernetes.io/server-snippet: |
      client_header_buffer_size 64k;
      large_client_header_buffers 4 128k;
spec:
  rules:
    - host: ouakahhgccqw.sealosbja.site
      http:
        paths:
          - pathType: Prefix
            path: /
            backend:
              service:
                name: devbox-release-ejzkwi
                port:
                  number: 8080
  tls:
    - hosts:
        - ouakahhgccqw.sealosbja.site
      secretName: wildcard-cert

```

##### Buildpacks

local repo one-click deploy to cloud

Remote dev environment is created from platform template(image), containing language SDK and init code. All parts is coherent and consistent, language/build tool/dependency info is clear, so building image and deployment is easy.

Local repo is not created by platform, environment and dependency is not clear. Buildpack transform your application source code into container images. Paketo Buildpacks auto-detect language by check file extension and special file (i.e. pom.xml, go.mod), install dependency, compile code and generate OCI image. Buildpack start builder container and mount code repo as container volume.

With buildpacks, you have full control over the OSes, languages, and package management your teams can use - no matter the size of your org.

A stack consists of two images:

- build image: the environment in which your app is built
- run image: the OS layer on which your app runs

A builder is an image that contains three components:

- a set of buildpacks, which provide your app’s dependencies
- a stack, which provides the OS layer for your app image
- the CNB lifecycle, which puts everything together to produce your final app image

GoogleCloudPlatform/buildpacks

- detect phase:

golang detect, opt-in if there is at least one .go file outside of the dependency directories

```go
func detectFn(ctx *gcp.Context) (gcp.DetectResult, error) {
	atLeastOne, err := ctx.HasAtLeastOneOutsideDependencyDirectories("*.go")
	if err != nil {
		return nil, fmt.Errorf("finding *.go files: %w", err)
	}
	if atLeastOne {
		return gcp.OptIn("found .go files"), nil
	}
	return gcp.OptOut("no .go files found"), nil
}
```

java detect

```go
func detectFn(ctx *gcp.Context) (gcp.DetectResult, error) {
	files := []string{
		"pom.xml",
		".mvn/extensions.xml",
		"build.gradle",
		"build.gradle.kts",
		"settings.gradle.kts",
		"settings.gradle",
		"META-INF/MANIFEST.MF",
	}
	for _, f := range files {
		exists, err := ctx.FileExists(f)
		if err != nil {
			return nil, err
		}
		if exists {
			return gcp.OptInFileFound(f), nil
		}
	}

	javaFiles, err := ctx.Glob("*.java")
	if err != nil {
		return nil, fmt.Errorf("finding .java files: %w", err)
	}
	if len(javaFiles) > 0 {
		return gcp.OptIn("found .java files"), nil
	}
	jarFiles, err := ctx.Glob("*.jar")
	if err != nil {
		return nil, fmt.Errorf("finding .jar files: %w", err)
	}
	if len(jarFiles) > 0 {
		return gcp.OptIn("found .jar files"), nil
	}
	return gcp.OptOut(fmt.Sprintf("none of the following found: %s, *.java, *.jar", strings.Join(files, ", "))), nil
}
```

- build phase:

runtime install

```go
func buildFn(ctx *gcp.Context) error {
	version, err := golang.RuntimeVersion(ctx)
	if err != nil {
		return err
	}
	grl, err := ctx.Layer(goLayer, gcp.BuildLayer, gcp.CacheLayer, gcp.LaunchLayerIfDevMode)
	if err != nil {
		return fmt.Errorf("creating layer: %w", err)
	}
  // installs a runtime tarball hosted on dl.google.com into the provided layer
	_, err = runtime.InstallTarballIfNotCached(ctx, runtime.Go, version, grl)
	return err
}
```

download dependency

```go
func buildFn(ctx *gcp.Context) error {
	
	if _, err := golang.ExecWithGoproxyFallback(ctx, []string{"go", "mod", "download"}, gcp.WithEnv(env...), gcp.WithUserAttribution); err != nil {
		return fmt.Errorf("running go mod download: %w", err)
	}

	return nil
}
```

build binary

```go

func buildFn(ctx *gcp.Context) error {
	
  // Create a layer for the compiled binary.  Add it to PATH in case
	// users wish to invoke the binary manually.
	bl, err := ctx.Layer("bin", gcp.LaunchLayer)
	if err != nil {
		return fmt.Errorf("creating layer: %w", err)
	}
	bl.LaunchEnvironment.Prepend("PATH", string(os.PathListSeparator), bl.Path)
	outBin := filepath.Join(bl.Path, golang.OutBin)
  
	bld := []string{"go", "build"}
	bld = append(bld, goBuildFlags()...)
	bld = append(bld, "-o", outBin)
	bld = append(bld, buildable)
	// BuildDirEnv should only be set by App Engine buildpacks.
	workdir := os.Getenv(golang.BuildDirEnv)
	if workdir == "" {
		workdir = ctx.ApplicationRoot()
	}
	if _, err := ctx.Exec(bld, gcp.WithEnv("GOCACHE="+cl.Path), gcp.WithWorkDir(workdir), gcp.WithMessageProducer(printTipsAndKeepStderrTail(ctx)), gcp.WithUserAttribution); err != nil {
		return err
	}
}
```





