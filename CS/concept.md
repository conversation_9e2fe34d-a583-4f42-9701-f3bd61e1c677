## front end, back end, client side, server side

front-end and client-side overlap (presentation layer, user-friendly interface, manipulated by the user). server-side and back-end also overlap (data access layer, physical infrastructure, business logic, far from the user).

- client-side is an application that runs at the users' computer. It could be a stand-alone application (more often) or it could refer to a web browser based interface (less likely).
- front-end also faces the end-user and generally runs in a web browser based interface. I haven't heard of thick clients being referred to as a front-end.
- back-end refers to processes and services that are running either on another server or in the background of the users' computer. More often than not, it refers to processes that are not on the end users' computer. But the key is that the end user is not necessarily aware of the processes running.
- server-side is an extension of back-end but explicitly reinforces the fact that the processes are running somewhere else and not on the end users' computers.

## Cloud Computing

- On-demand self-service. A consumer can unilaterally provision computing capabilities, such as server time and network storage, as needed automatically
- Broad network access. Capabilities are available over the network and accessed through standard mechanisms that promote use by heterogeneous thin or thick client platforms (e.g., mobile phones, tablets, laptops, and workstations).
- Resource pooling. The provider’s computing resources are pooled to serve multiple consumers using a multi-tenant model, with different physical and virtual resources dynamically assigned and reassigned according to consumer demand. There is a sense of location independence in that the customer generally has no control or knowledge over the exact location of the provided resources but may be able to specify location at a higher level of abstraction (e.g., country, state, or datacenter).
- Rapid elasticity. Capabilities can be elastically provisioned and released, in some cases automatically, to scale rapidly outward and inward commensurate with demand
- Measured service. Cloud systems automatically control and optimize resource use by leveraging a metering capability

## IaaS (Infrastructure-as-a-Service)

IaaS, is a form of cloud computing that delivers fundamental compute, network, and storage physical and virtualized resources to consumers on-demand, over the internet, and on a pay-as-you-go basis. IaaS enables end users to scale and shrink resources on an as-needed basis, reducing the need for high, up-front capital expenditures or unnecessary “owned” infrastructure, especially in the case of “spiky” workloads.

Bare-metal-as-a-Service (BMaaS) provides an even lower level of control than traditional IaaS. In a BMaaS environment, resources are still provisioned on-demand, made available over the internet, and billed on a pay-as-you-go basis. Unlike traditional IaaS, BMaaS does not provide end users with already virtualized compute, network, and storage; instead, it gives direct access to the underlying hardware.

IaaS, PaaS, and SaaS each offer a progressive layer of abstraction after that. IaaS abstracts away the physical compute, network, storage, and the technology needed to virtualize those resources. PaaS goes a step further and abstracts away the management of the operating system, middleware, and runtime. SaaS provides the entire end-user application as-a-Service, abstracting away the entire rest of the stack.

differences between IaaS, PaaS, and SaaS
![differences between IaaS, PaaS, and SaaS](../images/iaas-min.png)

## Real-time operating system

| Event-driven | Time-sharing|
| ------------- | ---------------|
| deterministic | non-deterministic |
| real-time OS  | time-sharing OS, Unix|
| circuit switch | packet switch |
| monopoly, starve| sharing, competitive |
| predictable delay | no guarantee |
| preemptive priority: switches tasks only when higher priority event comes, starvation | round-robin: switches tasks on clock or event interrupt, all can run, higher priority task receive more serving time via non-preemptive weighted-fair schedule, more switch more fair more switch cost less overall performance |

## Serverless

Serverless is an approach to computing that offloads responsibility for common infrastructure management tasks (e.g., scaling, scheduling, patching, monitoring, provisioning, runtimes and even JVM & dependent Jars etc.) to cloud providers and tools, allowing engineers to focus their time and effort on the business logic specific to their applications or process without managing the underlying servers (NoOps)

There are still servers in serverless computing, the name is describing an end user’s experience, the management needs of the underlying servers are invisible to the end user.

Functions-as-a-Service (FaaS) is actually a subset of serverless. Serverless is focused on any service category, be it compute, storage, database, etc. FaaS is focused on the event-driven computing paradigm wherein application code, or containers, only run in response to events or requests (pay-per-use billing).

## Database

### NoSQL

NoSQL, which stands for “not only SQL,” is an approach to database design that provides flexible schemas for the storage and retrieval of data beyond the traditional row/column/table structures found in relational databases. The most common types of NoSQL databases are key-value, document, column and graph databases.

Some specific cases when NoSQL databases are a better choice than RDBMS include the following:

- When you need to store large amounts of unstructured data with changing schemas. NoSQL databases usually have horizontal scaling properties that allow them to store and process large amounts of data. And NoSQL enables ad-hoc schema changes.
- When you’re using cloud computing and storage. Most NoSQL databases are designed to be scaled across multiple data centers and run as distributed systems
- When you need to develop rapidly. NoSQL is often the data store of choice for Agile software development methods. With NoSQL, instead of having to migrate structured data every time the application design changes, a dynamic NoSQL schema can evolve with the application.
- When a hybrid data environment makes sense. NoSQL is sometimes taken to mean not only SQL

### Redis

Redis (for REmote DIctionary Server) is an open source, in-memory, NoSQL key/value store that is used primarily as an application cache or quick-response database.

### etcd


etcd is an open source, strongly consistent, distributed key-value store used to hold and manage the critical information that distributed systems need to keep running. Most notably, it manages the configuration data, state data, and metadata for Kubernetes, the popular container orchestration platform.

The name “etcd” comes from a naming convention within the Linux directory structure: In UNIX, all system configuration files for a single system are contained in a folder called “/etc;” “d” stands for “distributed.”

etcd & distributed locking

```go
sess, _  := concurrency.NewSession(cli, concurrency.WithTTL(10))
mutex    := concurrency.NewMutex(sess, "/my-lock-prefix/")

// Lock creates a key under the prefix with a sequential suffix (/my-lock-prefix/0000000003), tied to the session
// The client then watches the key immediately preceding its own (e.g., 0000000002) and blocks until that key is deleted (i.e., its holder releases or its session expires)
// Unlock removes the client’s key, which triggers the next waiter’s watch to fire, granting them the lock
// when session ends (either via sess.Close() or lease expiry), keys attached to a session are removed
if err := mutex.Lock(ctx); err != nil {
    // handle acquisition error
}

if err := mutex.Unlock(ctx); err != nil {
    // handle release error
}

```

Distributed locking in etcd relies not only on leases and ordered keys but also on fencing tokens—monotonically increasing identifiers that guard against “zombie” clients performing operations after their lock has expired. The client must attach this token to any subsequent operations on the protected resource. Downstream systems then reject requests whose token is older than the last seen, ensuring that only the most recent lock holder can act. 

![unsafe](../images/unsafe-lock.png)

![sage](../images/fencing-tokens.png)


## LLVM

The most important aspect of its design is the LLVM Intermediate Representation (IR), which is the form it uses to represent code in the compiler.

With this design, porting the compiler to support a new source language (e.g., Algol or BASIC) requires implementing a new front end, but the existing optimizer and back end can be reused. If these parts weren't separated, implementing a new source language would require starting over from scratch, so supporting N targets and M source languages would need N\*M compilers.

Retargetablity
![Retargetablity](../images/LLVMCompiler1.png)

## proxy and reverse proxy

### proxy

A forward proxy, often called a proxy, proxy server, or web proxy, is a server that sits in front of a group of client machines. When those computers make requests to sites and services on the Internet, the proxy server intercepts those requests and then communicates with web servers on behalf of those clients, like a middleman.

![forward proxy](../images/forward-proxy-flow.png)

Why would anyone add this extra middleman to their Internet activity? There are a few reasons one might want to use a forward proxy:

- bypass GFW, i.e. clash, TOR
- filter contents
- hide users behind proxy, invisible

### reverse proxy

A reverse proxy is a server that sits in front of one or more web servers, intercepting requests from clients. This is different from a forward proxy, where the proxy sits in front of the clients. With a reverse proxy, when clients send requests to the origin server of a website, those requests are intercepted at the network edge by the reverse proxy server. The reverse proxy server will then send requests to and receive responses from the origin server.

![reverse proxy](../images/reverse-proxy-flow.png)

Below we outline some of the benefits of a reverse proxy:

- Load balancing
- Protect servers from attacks (i.e. DDoS)
- Caching
- relieve servers of the computationally expensive SSL encryption

#### ngrok

Reverse proxy that creates a secure tunnel from a public endpoint to a locally running web service, giving you a secure way to access your local service from anywhere in the world.

##### Step 1: Starting a local web service

```bash
mkdir ~/ngrok-rocks
cd ~/ngrok-rocks
vi index.html
# insert "Hello, World!"
python3 -m http.server # http://localhost:8000
```

Unfortunately, this service is only available on your local machine for now. Now let's use ngrok to securely share it with the world

##### Step 2: Install the ngrok Agent

```bash
brew install ngrok
ngrok -h
```

##### Step 3: Connect your agent to your ngrok account

sign up (or log in) to the ngrok Dashboard and get your Authtoken `TOKEN` and `ngrok config add-authtoken TOKEN`

##### Step 4: Start ngrok

```bash

# export https_proxy=http://127.0.0.1:7890;export http_proxy=http://127.0.0.1:7890;export all_proxy=socks5://127.0.0.1:7890
# export http proxy fail, because only when proxy env vars are recognized by app proxy will work well, ngrok app may not consider use proxy for agent to connect to server
# use Clash for Windows TUN mode instead, which intercept all app traffic without recognition, warp it and send into tunnel

ngrok http http://localhost:8000
# Session Status                online
# Account                       changren-wcr (Plan: Free)
# Version                       3.0.3
# Region                        Japan (jp)
# Latency                       calculating...
# Web Interface                 http://127.0.0.1:4040
# Forwarding                    https://20eb-125-33-204-60.jp.ngrok.io -> http://localhost:8000

# use ngrok static domain
ngrok http --url noted-raven-virtually.ngrok-free.app http://localhost:8000

# use your domain
# 1. register the domain name example.com with a DNS registrar
# 2. create canonical name xxx.ngrok-cname.com for your service endpoint in ngrok
# 3. create a new CNAME record (foo.example.com: xxx.ngrok-cname.com) in DNS registrar

# access, note that even when your PC is locked, website still works. you can deploy private-hosted service, such as podsync (Turn YouTube channels into podcast feeds)
curl noted-raven-virtually.ngrok-free.app
```

Now open the Forwarding URL in your browser and you should see your local web service. That URL is available to anyone in the world.

##### tunnel over ssh without downloading or running the ngrok agent

```bash
# add local ssh public key to ngrok dashboard
go tool pprof -http :1234 .pb.gz
ssh -R 443:localhost:1234 tunnel.ngrok.com http
# Warning: Permanently added 'tunnel.ngrok.com,2406:da14:540:e901::6e74:1' (RSA) to the list of known hosts.
#
# ngrok (via SSH) (Ctrl+C to quit)
#
# Account     changren-wcr (Plan: Free)
# Region      jp
# Forwarding  https://0c4b-2408-8207-251b-7da0-a6c7-4bff-fe5e-8ce5.jp.ngrok.io
# Forwarding  http://0c4b-2408-8207-251b-7da0-a6c7-4bff-fe5e-8ce5.jp.ngrok.io
```

##### How secure tunnels works

an ngrok tunnel is always initiated on the client-side first: ngrok Secure Tunnels work by using a locally installed ngrok agent to establish a connection to the ngrok service. Once the connection is established, you get a public endpoint that you or others can use to access your local service.

When a user hits the public ngrok endpoint, the ngrok edge figures out where to route the request to and forwards it over an encrypted connection to the locally running ngrok agent. From there, the local ngrok agent takes care of sending traffic to your upstream service.

#### ArchiveBox

Set up ArchiveBox

```bash
brew tap archivebox/archivebox
brew install archivebox
```

Archive it

```bash
mkdir ~/Documents/test/archive
cd ~/Documents/test/archive
archivebox init
archivebox status
# To create an admin user,
archivebox manage createsuperuser
# To add new links, you can run:
archivebox add https://github.com/ArchiveBox/ArchiveBox/wiki/Quickstart
# To view your archive index, run:
archivebox server  # then visit http://127.0.0.1:8000
ngrok http 8000 # open local service to outside
```

## File storage, block storage, or object storage?

### Block storage

Block storage breaks a file into equally-sized chunks (or blocks) of data and stores each block separately under a unique address, blocks can be stored wherever it is most efficient in SAN.

Block storage allows for the creation of raw storage volumes, presents block storage to other networked systems as if those blocks were locally attached devices and lets you use block storage for almost any kind of application.

To access any file, the server's operating system uses the unique address to pull the blocks back together into the file, which takes less time than navigating through directories and file hierarchies to access a file. This creates multiple paths to the data and allows the user to retrieve/reassemble it quickly.

### File storage

File storage organizes and represents data as a hierarchy of files in folders, popular storage technique for decades. Hierarchical file storage works well with easily organized amounts of structured data. But, as the number of files grows, the file retrieval process can become cumbersome and time-consuming. If you need to store very large or unstructured data volumes, you should consider block-based or object-based storage.

Similar to an on-site file storage system (DAS), cloud-based file storage (NAS) allows multiple users to share the same file data.

### Object storage

suitable for unstructured data (data that does not conform to a traditional relational database with rows and columns. This includes email, videos, photos, web pages, audio files).

It offers a level of scalability not possible with traditional file- or block-based storage. Objects are discrete units of data that are stored in a structurally flat data environment. There are no folders, directories, or complex hierarchies as in a file-based system. Each object is a simple, self-contained repository that includes the data, metadata (descriptive information associated with an object), and a unique identifying ID number (instead of a file name and file path). This information enables an application to locate and access the object.

You can aggregate object storage devices into larger storage pools and distribute these storage pools across locations (cloud servers). This allows for unlimited scale, as well as improved data resiliency and disaster recovery.

Objects (data) in an object-storage system are accessed via Application Programming Interfaces (APIs). The native API for object storage is an HTTP-based RESTful API (also known as a RESTful Web service). RESTful APIs use HTTP commands like “PUT” or “POST” to upload an object, “GET” to retrieve an object, and “DELETE” to remove it.

It can scale easily. Objects can’t be modified—you have to write the object completely at once.

## chaos engineering

Chaos Engineering is the discipline of experimenting on a system in order to build confidence in the system’s capability to withstand turbulent conditions in production. We need to identify weaknesses before they manifest in system-wide, aberrant behaviors. We must address the most significant weaknesses proactively, before they affect our customers in production.

## zero knowledge proof

The essence of zero-knowledge proofs is that it is trivial to prove that one possesses knowledge of certain information by simply revealing it; the challenge is to prove such possession without revealing the information itself or any additional information.

For example, if two children, Alice and Bob, want to see if they have
received the same number of Halloween candies without showing each
other their respective candy collections, they can use the following
zero-knowledge proof implementation.

- Bob can label each of four locked boxes with different numbers. Only one box will be labeled with
  the number of candies that Bob has. He will keep the key to that box, Alice can not see
- Bob will throw away the keys to all the other boxes. Alice verify that three keys are thrown
- Alice will then slip identical pieces of paper into each box. If Alice sees a box labeled with
  the number of candies she holds, she will place a special mark on the
  paper she places in that box. Bob can not see
- If Bob then opens up the only box he has
  a key to, and takes out the paper. Alice can not see
- Alice and Bob both see whether there is mark on the paper, and will know they have
  the same number of candies or not

## jargon

### Dot file

for example, `.config`, `.profile`, `ssh`. a hidden folder or hidden file is a folder or file which filesystem utilities do not display by default when showing a directory listing. They are commonly used for storing user preferences or preserving the state of a utility, access to dot files is not restricted.

Your dotfiles are how you personalize your system.

### blob

A Binary Large OBject (BLOB) is a collection of binary data stored as a single entity. Blobs are typically images, audio or other multimedia objects, though sometimes binary executable code is stored as a blob.

### .so, .a, .dll

- .so (shared object), dynamic library
- .a (archive), static library
- .dll (dynamic link library), shared library of Windows OS

## CNCF (Cloud Native Computing Foundation)

CNCF was announced alongside Kubernetes (container-packaged, dynamically-scheduled and microservices-oriented)

![CNCF](../images/CNCF_TrailMap_latest.png)

### containerd

container daemon provides a client layer of types that platforms can build on top of without ever having to drop down to OS specific level
![containerd](../images/containerd.png)

### CoreDNS

With containers of microservice come and go, tracking where a particular service is running can be challenging.
CoreDNS is used to support the service discovery function in containerized environments, particularly those managed by Kubernetes.

### Envoy

proxy runs alongside every application and abstracts the network by providing common features in a platform-agnostic manner.

### etcd

is a strongly consistent, distributed key-value store that provides a reliable way to store data that needs to be accessed by a distributed system or cluster of machines

### Fluentd

legacy logging infrastructure was NOT designed to be "machine-first". Most existing log formats have very weak structures. Arbitrarily formatted texts are terrible at analyzing logs because computers are terrible at parsing and extracting information from them

In the last 10 years, the primary consumer of log data shifted from humans to machines. Define an interface that all log producers and consumers implement against. This is the first requirement for the Unified Logging Layer. Fluentd decouples data sources from backend systems by providing a unified logging layer in between

### Harbor

Harbor is an open source registry that secures artifacts with policies and role-based access control, ensures images are scanned and free from vulnerabilities, and signs images as trusted.

### Helm

Helm is the best way to find, share, and use software built for Kubernetes (The package manager for Kubernetes)

Helm manages Kubernetes manifests (YAML files) through Helm chart. A Helm chart is a collection of files that describe a Kubernetes application.

- Chart.yaml: metadata about the chart (name, version, etc.)
- values.yaml: default configuration values
- templates/: folder containing Kubernetes manifest templates (e.g., Deployment, Service, etc.)

Helm uses Go templates to allow dynamic creation of manifests. The values.yaml provides inputs to these templates, making deployments more configurable. Sends the rendered manifests all at once without explicit order to the Kubernetes API server.

K8s does not explicitly manage dependencies between resources, it follows a declarative and eventually consistent model. Each resource (Deployment, Service, ConfigMap, etc.) is handled by its respective controller and reconciled to its desired state. If a resource depends on another that is not yet ready, it may enter a CrashLoopBackOff, Pending, or Failed state temporarily.

### Jaeger

Jaeger is a distributed tracing system used for monitoring and troubleshooting microservices-based distributed systems

### Kubernetes

Kubernetes, also known as K8s, is an open-source system for automating deployment, scaling, and management of containerized applications

### Open Policy Agent

A policy is a set of rules that governs the behavior of a software service. That policy could describe rate-limits, names of trusted servers, the clusters an application should be deployed to, permitted network routes, or accounts a user can withdraw money from.

Authorization is a special kind of policy that often dictates which people or machines can run which actions on which resources.

Authorization is sometimes confused with Authentication: how people or machines prove they are who they say they are.

Authorization and more generally policy often utilize the results of authentication (the username, user attributes, groups, claims), but makes decisions based on far more information than just who the user is.

Use OPA for a unified toolset and framework for policy across the cloud native stack. Use OPA to decouple policy from the service's code so you can release, analyze, and review policies without sacrificing availability or performance.

OPA decouples policy decision-making from policy enforcement. When your software needs to make policy decisions it queries OPA and supplies structured data (e.g., JSON) as input. OPA generates policy decisions by evaluating the query input and against policies and data.

![opa](../images/opa-service.png)

### Rook

Rook is an open source cloud-native storage orchestrator. Rook turns storage software into self-managing, self-scaling, and self-healing storage services. It does this by automating deployment, bootstrapping, configuration, provisioning, scaling, upgrading, migration, disaster recovery, monitoring, and resource management.

### Vitess

Vitess is a database solution for deploying, scaling and managing large clusters of MySQL instances

### TUF

The Update Framework (TUF) helps developers maintain the security of a software update system, even against attackers that compromise the repository or signing keys

### TiKV

TiKV ("Ti" stands for Titanium) is a distributed transactional key-value database

### Contour

an open source Kubernetes ingress (Exposes one or more services to external clients
through a single externally reachable IP address) controller providing the control plane for the Envoy edge and service proxy

## hyperconverged infrastructure (HCI) Software-Defined Cloud Centers (SDCC)

Hyper-converged infrastructure is a software-defined IT infrastructure that virtualizes all of the elements of conventional "hardware-defined" systems which will lead to several types of optimization.

It is a huge leap in the IT world as it marks the transition of computing to an era where data center components are abstracted from the underlying hardware. There is virtualization in every aspect starting from compute to network to storage.

Besides virtualization, containerization is the popular mechanism for software-enabling IT infrastructures.

## nodejs

Node.js has a unique advantage because millions of frontend developers that write JavaScript for the browser are now able to write the server-side code without the need to learn a completely different language. Node.js apps bring the comfort of programming everything - the frontend and the backend - in a single language.

Building apps that run in the browser is a completely different thing than building a Node.js application. In the browser, most of the time what you are doing is interacting with the DOM (Document Object Model). You don't have the document, window and all the other objects that are provided by the browser in Node.js. And in the browser, we don't have all the nice APIs that Node.js provides through its modules, like the filesystem access functionality.

npm is the default package manager for Node.js.

Node.js is an asynchronous event-driven concurrent (single process without creating a new thread for every request, no locks and deadlocks) JavaScript runtime environment. When Node.js performs an I/O operation, instead of blocking the thread and wasting CPU cycles waiting, Node.js will resume the operations when the response comes back.

V8 is the name of the JavaScript engine that powers Google Chrome. The cool thing is that the JavaScript engine is independent of the browser in which it's hosted. This key feature enabled the rise of Node.js. JavaScript is generally considered an interpreted language, but JavaScript is internally compiled by V8 with just-in-time (JIT) compilation to speed up the execution.

The Electron framework lets you write cross-platform desktop applications using JavaScript, HTML and CSS. It is based on Node.js and Chromium and is used by the GitHub Desktop, Visual Studio Code, Evernote and many other apps.

## zero-day

A zero-day exploit is when hackers take advantage of a software security flaw to perform a cyberattack. And that security flaw is only known to hackers, meaning software developers have no clue to its existence and have no patch to fix it.

This is why, when a zero-day attack is detected, it needs to be mitigated immediately. In other words, there are “zero days” to fix vulnerability because it’s already been exploited.

## Software supply chain threats

![supply-chain](../images/software-supply-chain.svg)